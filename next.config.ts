import type { NextConfig } from "next";

const nextConfig: NextConfig = {
	/* config options here */
	output: "standalone",
	images: {
		remotePatterns: [
			{
				protocol: "https",
				hostname: "lh3.googleusercontent.com",
				port: "",
			},
			{
				protocol: "https",
				hostname: "static.voicedesi.com",
				port: "",
			},
			{
				protocol: "https",
				hostname: "storage.googleapis.com",
				port: "",
			},
			{
				protocol: "https",
				hostname: "fal.media",
				port: "",
			},
		],
	},
	headers: async () => [
		{
			source: "/:path*",
			headers: [
				{
					key: "frame-ancestors",
					value: "none",
				},
			],
		},
	],
};

export default nextConfig;
