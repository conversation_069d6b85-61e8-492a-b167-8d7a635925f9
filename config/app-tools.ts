import { KLING_2_1_STANDARD, LTX_13B_DISTILLED, PIXVERSE_4_5, SEEDANCE_1_LITE } from "@/config/video-models-config";
import { LucideIcon, FileImage, Video, ImageIcon, FilmIcon, PaletteIcon, TypeIcon, ToolCaseIcon, BookImageIcon } from "lucide-react";

export type AppModelType = {
	name: string;
	url: string;
	new?: boolean;
};
export const imageModels: AppModelType[] = [
	{
		name: "Flux",
		url: "/flux",
	},
	{
		name: "Flux Krea",
		url: "/flux/flux-krea",
		new: true,
	},
	{
		name: "Flux Dev",
		url: "/flux",
	},
	{
		name: "Flux 1.1 Pro",
		url: "/flux",
	},
	{
		name: "Flux 1.1 Pro Ultra",
		url: "/flux",
	},
	{
		name: "Flux Kontext",
		url: "/flux/flux-kontext",
	},
	{
		name: "Imagen 4",
		url: "/imagen",
	},
	{
		name: "Ideogram 3",
		url: "/ideogram",
	},
	{
		name: "Recraft 3",
		url: "/recraft",
	},
	{ name: "Seedream 3", url: "/ai-image-generator?model=seedream-3" },
	{
		name: "HiDream",
		url: "/hidream",
	},
	{
		name: "Gemini Flash",
		url: "/gemini",
	},
];
export const videoModels: AppModelType[] = [
	{
		name: "Veo 3",
		url: "/veo",
	},
	{
		name: "Kling 2.1",
		url: `/image-to-video?model=${KLING_2_1_STANDARD.id}`,
	},
	{ name: "Pixverse 4.5", url: `/image-to-video?model=${PIXVERSE_4_5.id}` },
	{ name: "Hailuo 2", url: "/hailuo" },
	{ name: "Seedance 1", url: `/image-to-video?model=${SEEDANCE_1_LITE.id}` },
	{ name: "Wan 2.2", url: "/wan", new: true },
	{ name: "LTX Video", url: `/image-to-video?model=${LTX_13B_DISTILLED.id}` },
];

export type AppToolsType = {
	title: string;
	tools: {
		title: string;
		description?: string;
		url: string;
		icon: LucideIcon;
	}[];
};
export const imageAITools: AppToolsType = {
	title: "Image AI",
	tools: [
		{
			title: "Image Generator",
			description: "Text to image generation",
			url: "ai-image-generator",
			icon: TypeIcon,
		},
		{
			title: "Image to Image",
			description: "Image to image generation",
			url: "/image-to-image",
			icon: ImageIcon,
		},
		{
			title: "Image Editor",
			description: "Edit images with AI",
			url: "/image-editor",
			icon: FileImage,
		},
		{
			title: "Image Tools",
			description: "Explore image AI tools",
			url: "/image-tools",
			icon: ToolCaseIcon,
		},
		{
			title: "Image Generators",
			description: "Explore image generators",
			url: "/image-generators",
			icon: BookImageIcon,
		},
		{
			title: "Photo Effects",
			description: "Transform your photos with AI",
			url: "/photo-effects",
			icon: PaletteIcon,
		},
	],
};
export const videoAITools: AppToolsType = {
	title: "Video AI",
	tools: [
		{
			title: "Image to Video",
			description: "Image to video generation",
			url: "/image-to-video",
			icon: FilmIcon,
		},
		{
			title: "Text to Video",
			description: "Text to video generation",
			url: "/ai-video-generator",
			icon: Video,
		},
	],
};
