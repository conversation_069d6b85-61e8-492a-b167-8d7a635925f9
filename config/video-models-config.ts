import { OSS_URL_HOST } from "@/lib/constants";
import { RectangleVertical, Square, RectangleHorizontal } from "lucide-react";

export const getAspectRatioIcon = (aspectRatio: string) => {
	switch (aspectRatio) {
		case "16:9":
			return RectangleHorizontal;
		case "9:16":
			return RectangleVertical;
		case "1:1":
			return Square;
		case "9:21":
			return RectangleVertical;
		default:
			throw new Error("Invalid aspect ratio");
	}
};
export const getAspectRatioClass = (aspectRatio: string) => {
	switch (aspectRatio) {
		case "16:9":
			return "aspect-video";
		case "9:16":
			return "aspect-9/16";
		case "1:1":
			return "aspect-square";
		case "9:21":
			return "aspect-9/21";
		default:
			throw new Error("Invalid aspect ratio");
	}
};

export enum VedioModelLogo {
	Veo = `${OSS_URL_HOST}icon/model/deepmind-color.webp`,
	Kling = `${OSS_URL_HOST}icon/model/kling-color.webp`,
	Hailuo = `${OSS_URL_HOST}icon/model/hailuo-color.webp`,
	Pixverse = `${OSS_URL_HOST}icon/model/pixverse-color.webp`,
	Seedance = `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
	Wan = `${OSS_URL_HOST}icon/model/qwen-color.webp`,
	LTX = `${OSS_URL_HOST}icon/model/lightricks-light.webp`,
}

type CreditsType = {
	perSecond?: number;
	onetime?: number;
	per_second_with_resolution?: {
		resolution: string;
		per_second: number;
	}[];
	onetime_with_resolution?: {
		resolution: string;
		onetime: number;
	}[];
};
export const getVideoModelCredits = (creditsType: CreditsType, creditsMethod: string, duration?: number, resolution?: string | null) => {
	if (creditsMethod === "per_second") {
		return duration! * creditsType.perSecond!;
	}
	if (creditsMethod === "onetime") {
		return creditsType.onetime!;
	}
	if (creditsMethod === "per_second_with_resolution") {
		const resolutionCredits = creditsType.per_second_with_resolution?.find((r) => r.resolution === resolution);
		if (!resolutionCredits) {
			throw new Error("Invalid resolution");
		}
		return duration! * resolutionCredits.per_second;
	}
	if (creditsMethod === "onetime_with_resolution") {
		const resolutionCredits = creditsType.onetime_with_resolution?.find((r) => r.resolution === resolution);
		if (!resolutionCredits) {
			throw new Error("Invalid resolution");
		}
		return resolutionCredits.onetime;
	}
	throw new Error("Invalid credits method");
};
export type VideoModel = {
	name: string;
	id: string; // for api, db
	model: string; // for image generatiton third platform api
	description?: string;
	modelStyle?: {
		id: string | null;
		name: string;
	}[];
	textToVideo: boolean;
	startFrame: boolean;
	endFrame: boolean;
	aspectRatioAll?: string[];
	durationAll: number[];
	resolutionAll?: string[];
	credits: CreditsType;
	creditsMethod: "per_second" | "onetime" | "per_second_with_resolution" | "onetime_with_resolution";
	// textToVideo?: {
	// 	aspectRatioAll?: string[];
	// 	durationAll: number[];
	// 	resolutionAll?: string[];
	// 	credits: CreditsType;
	// 	creditsMethod: "per_second" | "onetime" | "per_second_with_resolution" | "onetime_with_resolution";
	// };
	// imageToVideo?: {
	// 	aspectRatioAll?: string[];
	// 	durationAll: number[];
	// 	resolutionAll?: string[];
	// 	credits: CreditsType;
	// 	creditsMethod: "per_second" | "onetime" | "per_second_with_resolution" | "onetime_with_resolution";
	// 	endFrame?: boolean;
	// };
	noAspectRatio?: "all" | "image-to-video";
	time?: number; // seconds
	logo?: string;
	new?: boolean;
	pro?: boolean;
};
export const LTX_13B_DISTILLED: VideoModel = {
	name: "LTX Video",
	id: "ltxv-13b-098-distilled",
	model: "ltxv-13b-098-distilled",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5],
	resolutionAll: ["480p", "720p"],
	credits: { perSecond: 3 },
	creditsMethod: "per_second",
	time: 30,
	logo: VedioModelLogo.LTX,
};
export const KLING_2_1_MASTER: VideoModel = {
	name: "kling 2.1 Master",
	id: "kling-2.1-master",
	model: "kling-2.1-master",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	resolutionAll: undefined,
	credits: { perSecond: 28 },
	creditsMethod: "per_second",
	noAspectRatio: "image-to-video",
	time: 60 * 5,
	logo: VedioModelLogo.Kling,
	pro: true,
};
export const KLING_2_1_PRO: VideoModel = {
	name: "kling 2.1 Pro",
	id: "kling-2.1-pro",
	model: "kling-2.1-pro",
	textToVideo: false,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: undefined,
	durationAll: [5, 10],
	resolutionAll: undefined,
	credits: { perSecond: 9 },
	creditsMethod: "per_second",
	noAspectRatio: "all",
	time: 60 * 3,
	logo: VedioModelLogo.Kling,
	pro: true,
};
export const KLING_2_1_STANDARD: VideoModel = {
	name: "kling 2.1 Standard",
	id: "kling-2.1-standard",
	model: "kling-2.1-standard",
	textToVideo: false,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: undefined,
	durationAll: [5, 10],
	resolutionAll: undefined,
	credits: { perSecond: 5 },
	creditsMethod: "per_second",
	noAspectRatio: "all",
	time: 90,
	logo: VedioModelLogo.Kling,
	pro: true,
};
export const KLING_2_MASTER: VideoModel = {
	name: "kling 2 Master",
	id: "kling-2-master",
	model: "kling-2-master",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	resolutionAll: undefined,
	credits: { perSecond: 28 },
	creditsMethod: "per_second",
	noAspectRatio: "image-to-video",
	time: 60 * 5,
	logo: VedioModelLogo.Kling,
	pro: true,
};
export const GOOGLE_VEO_3: VideoModel = {
	name: "Veo 3",
	id: "google-veo-3",
	model: "veo3",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [8],
	resolutionAll: ["720p", "1080p"],
	credits: { perSecond: 75 },
	creditsMethod: "per_second",
	noAspectRatio: "image-to-video",
	time: 60 * 2,
	logo: VedioModelLogo.Veo,
	pro: true,
};
export const GOOGLE_VEO_3_FAST: VideoModel = {
	name: "Veo 3 Fast",
	id: "google-veo-3-fast",
	model: "veo3/fast",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [8],
	resolutionAll: ["720p", "1080p"],
	credits: { perSecond: 40 },
	creditsMethod: "per_second",
	noAspectRatio: "image-to-video",
	time: 90,
	logo: VedioModelLogo.Veo,
	pro: true,
};
export const SEEDANCE_1_LITE: VideoModel = {
	name: "Seedance 1 Lite",
	id: "seedance-1-lite",
	model: "seedance/v1/lite",
	textToVideo: true,
	startFrame: true,
	endFrame: true,
	aspectRatioAll: ["16:9", "1:1", "9:16", "9:21"],
	durationAll: [3, 5, 8, 10],
	resolutionAll: ["720p"],
	credits: { perSecond: 4 },
	creditsMethod: "per_second",
	noAspectRatio: "image-to-video",
	time: 60,
	logo: VedioModelLogo.Seedance,
};
export const SEEDANCE_1_PRO: VideoModel = {
	name: "Seedance 1 Pro",
	id: "seedance-1-pro",
	model: "seedance/v1/pro",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "1:1", "9:16", "9:21"],
	durationAll: [3, 5, 8, 10],
	resolutionAll: ["720p", "1080p"],
	credits: {
		per_second_with_resolution: [
			{
				resolution: "720p",
				per_second: 9,
			},
			{
				resolution: "1080p",
				per_second: 13,
			},
		],
	},
	creditsMethod: "per_second_with_resolution",
	noAspectRatio: "image-to-video",
	time: 60 * 2,
	logo: VedioModelLogo.Seedance,
	pro: true,
};
export const PIXVERSE_4_5_FAST: VideoModel = {
	name: "Pixverse 4.5 Fast",
	id: "pixverse-4.5-fast",
	model: "pixverse/v4.5/fast",
	modelStyle: [
		{
			id: null,
			name: "Auto",
		},
		{
			id: "anime",
			name: "Anime",
		},
		{
			id: "3d_animation",
			name: "3D Animation",
		},
		{
			id: "clay",
			name: "Clay",
		},
		{
			id: "comic",
			name: "Comic",
		},
		{
			id: "cyberpunk",
			name: "Cyberpunk",
		},
	],
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5],
	resolutionAll: ["360p", "480p", "720p"],
	credits: {
		per_second_with_resolution: [
			{
				resolution: "360p",
				per_second: 6,
			},
			{
				resolution: "480p",
				per_second: 6,
			},
			{
				resolution: "720p",
				per_second: 8,
			},
		],
	},
	creditsMethod: "per_second_with_resolution",
	time: 100,
	logo: VedioModelLogo.Pixverse,
	pro: true,
};
export const PIXVERSE_4_5: VideoModel = {
	name: "Pixverse 4.5",
	id: "pixverse-4.5",
	model: "pixverse/v4.5",
	modelStyle: [
		{
			id: null,
			name: "Auto",
		},
		{
			id: "anime",
			name: "Anime",
		},
		{
			id: "3d_animation",
			name: "3D Animation",
		},
		{
			id: "clay",
			name: "Clay",
		},
		{
			id: "comic",
			name: "Comic",
		},
		{
			id: "cyberpunk",
			name: "Cyberpunk",
		},
	],
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 8],
	resolutionAll: ["360p", "480p", "720p", "1080p"],
	credits: {
		per_second_with_resolution: [
			{
				resolution: "360p",
				per_second: 3,
			},
			{
				resolution: "480p",
				per_second: 3,
			},
			{
				resolution: "720p",
				per_second: 4,
			},
			{
				resolution: "1080p",
				per_second: 8,
			},
		],
	},
	creditsMethod: "per_second_with_resolution",
	time: 100,
	logo: `${OSS_URL_HOST}icon/model/pixverse-color.webp`,
};
export const HAILUO_2_STANDARD: VideoModel = {
	name: "Hailuo 2 Standard",
	id: "hailuo-02-standard",
	model: "hailuo-02/standard",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: undefined,
	durationAll: [6, 10],
	resolutionAll: ["768p"],
	credits: {
		per_second_with_resolution: [
			{
				resolution: "768p",
				per_second: 5,
			},
		],
	},
	creditsMethod: "per_second_with_resolution",
	noAspectRatio: "all",
	time: 60 * 4,
	logo: VedioModelLogo.Hailuo,
	pro: true,
};
export const HAILUO_2_PRO: VideoModel = {
	name: "Hailuo 2 Pro",
	id: "hailuo-02-pro",
	model: "hailuo-02/pro",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: undefined,
	durationAll: [5],
	resolutionAll: ["1080p"],
	credits: { perSecond: 8 },
	creditsMethod: "per_second",
	noAspectRatio: "all",
	time: 60 * 6,
	logo: VedioModelLogo.Hailuo,
	pro: true,
};
// export const WAN_2_2_LITE: VideoModel = {
// 	name: "Wan 2.2 Lite",
// 	id: "wan-v2.2-5b",
// 	model: "wan/v2.2-5b",
// 	credits: { onetime: 15 },
// 	creditsMethod: "onetime",
// 	aspectRatioAll: ["16:9", "9:16", "1:1"],
// 	durationAll: [5],
// 	resolutionAll: ["580p", "720p"],
// 	textToVideo: true,
// 	imageToVideo: true,
// 	time: 30,
// 	logo: `${OSS_URL_HOST}icon/model/qwen-color.webp`,
// 	new: true,
// };
export const WAN_2_2_TURBO: VideoModel = {
	name: "Wan 2.2 Turbo",
	id: "wan-v2.2-a14b-turbo",
	model: "wan/v2.2-a14b-turbo",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5],
	resolutionAll: ["480p", "580p", "720p"],
	credits: {
		onetime_with_resolution: [
			{
				resolution: "480p",
				onetime: 6,
			},
			{
				resolution: "580p",
				onetime: 8,
			},
			{
				resolution: "720p",
				onetime: 11,
			},
		],
	},
	creditsMethod: "onetime_with_resolution",
	time: 40,
	logo: VedioModelLogo.Wan,
	new: true,
};
export const WAN_2_2: VideoModel = {
	name: "Wan 2.2",
	id: "wan-v2.2-a14b",
	model: "wan/v2.2-a14b",
	textToVideo: true,
	startFrame: true,
	endFrame: false,
	credits: { perSecond: 8 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5],
	resolutionAll: ["480p", "580p", "720p"],
	time: 60 * 3,
	logo: VedioModelLogo.Wan,
	new: true,
	pro: true,
};

const videoModels: VideoModel[] = [
	WAN_2_2_TURBO,
	WAN_2_2,
	LTX_13B_DISTILLED,
	GOOGLE_VEO_3,
	GOOGLE_VEO_3_FAST,
	KLING_2_1_MASTER,
	KLING_2_1_PRO,
	KLING_2_1_STANDARD,
	HAILUO_2_PRO,
	HAILUO_2_STANDARD,
	SEEDANCE_1_PRO,
	SEEDANCE_1_LITE,
	PIXVERSE_4_5,
	PIXVERSE_4_5_FAST,
	KLING_2_MASTER,
];
export const textToVideoModels: VideoModel[] = videoModels.filter((videoModel) => videoModel.textToVideo);
export const imageToVideoModels: VideoModel[] = videoModels.filter((videoModel) => videoModel.startFrame);

export type VideoWithSeriesModel = {
	name: string;
	id: string; // series id
	logo: string;
	description?: string;
	models: VideoModel[];
	new?: boolean;
};
export const videoWithSeriesModels: VideoWithSeriesModel[] = [
	{
		name: "Hailuo",
		id: "hailuo",
		logo: VedioModelLogo.Hailuo,
		models: [HAILUO_2_STANDARD, HAILUO_2_PRO],
	},
	{
		name: "Veo",
		id: "veo",
		logo: VedioModelLogo.Veo,
		models: [GOOGLE_VEO_3_FAST, GOOGLE_VEO_3],
	},
	{
		name: "Seedance",
		id: "seedance",
		logo: VedioModelLogo.Seedance,
		models: [SEEDANCE_1_LITE, SEEDANCE_1_PRO],
	},
	{
		name: "Kling",
		id: "kling",
		logo: VedioModelLogo.Kling,
		models: [KLING_2_1_STANDARD, KLING_2_1_PRO, KLING_2_1_MASTER],
	},
	{
		name: "Wan",
		id: "wan",
		logo: VedioModelLogo.Wan,
		models: [WAN_2_2_TURBO, WAN_2_2],
		new: true,
	},
	{
		name: "Pixverse",
		id: "pixverse",
		logo: VedioModelLogo.Pixverse,
		models: [PIXVERSE_4_5, PIXVERSE_4_5_FAST],
	},
	{
		name: "LTX",
		id: "ltx",
		logo: VedioModelLogo.LTX,
		models: [LTX_13B_DISTILLED],
	},
];

export const getVideoModel = (id: string): VideoModel => {
	const videoModel = videoModels.find((videoModel) => videoModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("videoModel: ", videoModel);
	}
	if (!videoModel) {
		throw new Error("Video model is not found.");
	}
	return videoModel;
};

export const getInitialVideoModel = (id: string | null | undefined): VideoModel => {
	if (!id) return WAN_2_2_TURBO;

	return videoModels.find((model) => model.id === id) || WAN_2_2_TURBO;
};

export const pageVideoModels: VideoModel[] = [
	HAILUO_2_STANDARD,
	HAILUO_2_PRO,
	GOOGLE_VEO_3_FAST,
	GOOGLE_VEO_3,
	SEEDANCE_1_LITE,
	SEEDANCE_1_PRO,
	KLING_2_1_STANDARD,
	KLING_2_1_PRO,
	KLING_2_1_MASTER,
	WAN_2_2_TURBO,
	WAN_2_2,
	PIXVERSE_4_5,
	PIXVERSE_4_5_FAST,
	LTX_13B_DISTILLED,
];

export const getVideoModelInPage = (id: string): VideoModel => {
	const videoModel = pageVideoModels.find((videoModel) => videoModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("videoModel: ", videoModel);
	}
	if (!videoModel) {
		throw new Error("Video model is not found.");
	}
	return videoModel;
};

export type ModelSeriesType = {
	name: string;
	description?: string;
	url: string;
	logo: string;
	new?: boolean;
};
export const videoModelSeries: ModelSeriesType[] = [
	{
		name: "Veo",
		url: "/models/video/veo",
		logo: VedioModelLogo.Veo,
	},
	{
		name: "Kling",
		url: `/image-to-video?model=${KLING_2_1_STANDARD.id}`,
		logo: VedioModelLogo.Kling,
	},
	{ name: "Hailuo", url: "/models/video/hailuo", logo: VedioModelLogo.Hailuo },
	{ name: "Pixverse", url: `/ai-video-generator?model=${PIXVERSE_4_5.id}`, logo: VedioModelLogo.Pixverse },
	{ name: "Seedance", url: `/ai-video-generator?model=${SEEDANCE_1_LITE.id}`, logo: VedioModelLogo.Seedance },
	{ name: "Wan", url: "/models/video/wan", logo: VedioModelLogo.Wan, new: true },
	{ name: "LTX Video", url: `/ai-video-generator?model=${LTX_13B_DISTILLED.id}`, logo: VedioModelLogo.LTX },
];
