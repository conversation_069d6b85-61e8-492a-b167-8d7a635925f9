import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { WEBNAME } from "@/lib/constants";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_GENERATE_VOICE } from "@/lib/track-events";
import { createMediaVoice, updateMediaVoice } from "@/server/utils-media.server";
import { getUUIDString } from "@/lib/utils";
import { ofetch } from "ofetch";
import { saveVoiceToR2 } from "@/server/r2.server";

type Params = {
	textPreview: string;
	customVoiceId?: string;
	emotion?: string;
	pitch?: number;
	speed?: number;
	volume?: number;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();

	if (!params.textPreview) {
		return NextResponse.json({ status: 400, message: "Required parameter textPreview is missing." });
	}

	try {
		const userId = await getSessionUserId();

		// Check user credits
		const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
			needCredits: 1,
		});

		// Track mixpanel event
		mixpanelTrackEvent(EVENT_GENERATE_VOICE, userId, {
			mp_country_code: cfIpCountryCode,
			membershipLevel: membershipLevel,
		});

		const uid = getUUIDString();

		// Create media voice record
		const mediaVoice = await createMediaVoice({
			uid: uid,
			userId: userId,
			status: MediaResultStatus.InProgress,
			customVoiceId: params.customVoiceId || "Wise_Woman", // Default voice
			visibility: false,
			creditsSources: JSON.stringify(creditConsumes),
			remark: `Voice generation for text: ${params.textPreview.substring(0, 50)}...`,
		});

		// Prepare Wavespeed API payload
		const wavespeedPayload = {
			emotion: params.emotion || "happy",
			english_normalization: false,
			pitch: params.pitch || 0,
			speed: params.speed || 1,
			text: params.textPreview,
			voice_id: params.customVoiceId || "Wise_Woman",
			volume: params.volume || 1,
		};

		if (process.env.NODE_ENV === "development") {
			console.log("Wavespeed API payload: ", wavespeedPayload);
		}

		// Call Wavespeed API
		const wavespeedResponse = await ofetch("https://api.wavespeed.ai/api/v3/minimax/speech-02-hd", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
			},
			body: wavespeedPayload,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("Wavespeed API response: ", wavespeedResponse);
		}

		const requestId = wavespeedResponse.data.id;

		// Poll for result
		let attempts = 0;
		const maxAttempts = 60; // 30 seconds max wait time
		let resultUrl = null;

		while (attempts < maxAttempts) {
			await new Promise((resolve) => setTimeout(resolve, 500)); // Wait 0.5 seconds

			try {
				const resultResponse = await ofetch(`https://api.wavespeed.ai/api/v3/predictions/${requestId}/result`, {
					headers: {
						Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
					},
				});

				if (resultResponse.data.status === "completed") {
					resultUrl = resultResponse.data.outputs[0];
					break;
				} else if (resultResponse.data.status === "failed") {
					throw new Error(resultResponse.data.error || "Voice generation failed");
				}
			} catch (error: any) {
				console.error("Error polling result:", error);
				if (attempts === maxAttempts - 1) {
					throw error;
				}
			}

			attempts++;
		}

		if (!resultUrl) {
			throw new Error("Voice generation timed out");
		}

		// Download and save the audio file to R2
		const audioResponse = await fetch(resultUrl);
		if (!audioResponse.ok) {
			throw new Error("Failed to download generated audio");
		}

		const audioBlob = await audioResponse.blob();
		const fileName = `voice-${uid}.mp3`;
		const mediaPath = await saveVoiceToR2(fileName, audioBlob);

		// Update media voice record with result
		await updateMediaVoice(uid, {
			status: MediaResultStatus.Completed,
			mediaPath: mediaPath,
		});

		// Update user credits
		await updateUserCredit(userId, creditConsumes, {
			remark: `Voice generation completed. Request ID: ${requestId}`,
		});

		return NextResponse.json({
			status: 200,
			message: "Success",
			resultUrl: {
				uid: uid,
				mediaPath: mediaPath,
				textPreview: params.textPreview,
				customVoiceId: params.customVoiceId || "Wise_Woman",
				description: `Generated voice for: ${params.textPreview.substring(0, 50)}...`,
			},
		});
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/voice/generate`);
	}
}
