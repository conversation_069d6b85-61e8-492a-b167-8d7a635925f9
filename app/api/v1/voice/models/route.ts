import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { handleApiError } from "@/@types/error-api";
import { WEBNAME } from "@/lib/constants";
import { getUserVoiceModels, getPublicVoiceModels } from "@/server/utils-media.server";

export async function GET(req: Request) {
	try {
		const sessionUser = await getCurrentSessionUser();
		const userId = sessionUser?.id || null;

		// // Get user's custom voice models (only if logged in)
		// const userVoiceModels = userId ? await getUserVoiceModels(userId) : [];

		// // Get public/preset voice models
		// const publicVoiceModels = await getPublicVoiceModels();

		// 测试数据
		// Predefined voice models (these would typically be in the database)
		const presetVoiceModels = [
			{
				id: "Energetic_Girl",
				customVoiceId: "Energetic_Girl",
				name: "Energetic Girl",
				description: "A lively and energetic female voice",
				mediaPath: "https://d1q70pf5vjeyhc.cloudfront.net/predictions/66bca63eb41c4859bb4d19f09567b3ee/1.mp3",
				visibility: true,
				userId: null,
				aiModelId: 1,
				languageCode: "en",
				textPreview: "Hello! This is an energetic girl voice sample.",
			},
		];

		// Combine all voice models
		const allVoiceModels = {
			library: presetVoiceModels,
			design: [],
		};
		// const allVoiceModels = {
		// 	library: presetVoiceModels,
		// 	user: userVoiceModels,
		// };

		return NextResponse.json({
			status: 200,
			message: "Success",
			data: allVoiceModels,
		});
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/voice/models`);
	}
}
