import type { Metada<PERSON> } from "next";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import VoiceDesignClient from "@/components/app/voice-design.client";

export const metadata: Metadata = {
	title: `${WEBNAME} - AI Voice Design`,
	description: "",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-20 pb-24">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 space-y-4 text-center sm:mx-auto">
							{/* <Link href="/flux/flux-kontext" className="cursor-pointer">
								<Announcement themed className="mb-4 bg-emerald-100 text-emerald-700">
									<AnnouncementTag className="group-[.announcement-themed]:bg-white/40">Latest update</AnnouncementTag>
									<AnnouncementTitle>
										FLUX.1 Kontext [dev] is now available!
										<ArrowUpRightIcon size={16} className="shrink-0 opacity-70" />
									</AnnouncementTitle>
								</Announcement>
							</Link> */}
							<h1 className="mx-auto max-w-4xl text-4xl font-semibold md:text-5xl">Al Voice Design</h1>
							<div className="text-muted-foreground mx-auto text-lg">Generate Unique Voices from Text Prompts</div>
						</div>
					</div>
					<div className="container mt-8 max-w-5xl px-6">
						<VoiceDesignClient />
					</div>
				</div>
			</section>
		</main>
	);
}
