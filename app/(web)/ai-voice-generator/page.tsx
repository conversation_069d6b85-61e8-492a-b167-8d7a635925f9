import type { Metadata } from "next";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import VoiceGenClient from "./voice-gen.client";

export const metadata: Metadata = {
	title: `AI Voice Generator | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/ai-voice-generator",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-20 pb-24">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 space-y-4 text-center sm:mx-auto">
							<h1 className="mx-auto max-w-4xl text-4xl font-semibold md:text-5xl">Al Voice Generator</h1>
							<div className="text-muted-foreground mx-auto text-lg"></div>
						</div>
					</div>
					<div className="container mt-8 max-w-5xl px-6">
						<VoiceGenClient />
					</div>
				</div>
			</section>
		</main>
	);
}
