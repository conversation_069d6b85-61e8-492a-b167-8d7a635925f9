"use client";

import { useState, useCallback, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { PlayIcon, PauseIcon, CheckIcon } from "lucide-react";
import { toast } from "sonner";
import { useWavesurfer } from "@wavesurfer/react";
import { OSS_URL_HOST } from "@/lib/constants";

interface VoiceModel {
	id: string;
	customVoiceId: string;
	name: string;
	description?: string;
	mediaPath?: string | null;
	textPreview?: string;
	visibility: boolean;
	userId?: string | null;
}

interface VoiceModelData {
	library: VoiceModel[];
	design: VoiceModel[];
}

interface VoiceModelDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	selectedVoiceId?: string;
	onSelectVoice: (voiceModel: VoiceModel) => void;
	voiceModels: VoiceModelData;
	loading: boolean;
}

export default function VoiceModelDialog({ open, onOpenChange, selectedVoiceId, onSelectVoice, voiceModels, loading }: VoiceModelDialogProps) {
	const [playingVoiceId, setPlayingVoiceId] = useState<string | null>(null);

	// Wavesurfer for audio preview
	const waveformRef = useRef<HTMLDivElement>(null);
	const { wavesurfer, isPlaying } = useWavesurfer({
		container: waveformRef,
		height: 60,
		waveColor: "#d4d4d8",
		progressColor: "#71717a",
		cursorColor: "#10b981",
		barWidth: 2,
		barGap: 1,
		barRadius: 2,
	});

	// No longer need to load voice models here - they come from props

	// Handle voice preview
	const handlePreview = useCallback(
		async (voiceModel: VoiceModel) => {
			if (playingVoiceId === voiceModel.customVoiceId) {
				// Stop current playback
				wavesurfer?.pause();
				setPlayingVoiceId(null);
				return;
			}

			try {
				let audioUrl = "";

				if (voiceModel.mediaPath) {
					audioUrl = voiceModel.mediaPath.startsWith("http") ? voiceModel.mediaPath : `${OSS_URL_HOST}${voiceModel.mediaPath}`;
				} else {
					// For now, we'll skip generating preview if no mediaPath exists
					toast.info("Preview not available for this voice model");
					return;
				}

				setPlayingVoiceId(voiceModel.customVoiceId);

				if (wavesurfer) {
					await wavesurfer.load(audioUrl);
					wavesurfer.play();

					wavesurfer.on("finish", () => {
						setPlayingVoiceId(null);
					});
				}
			} catch (error: any) {
				console.error("Failed to preview voice:", error);
				toast.error("Failed to preview voice");
				setPlayingVoiceId(null);
			}
		},
		[playingVoiceId, wavesurfer],
	);

	// Handle voice selection
	const handleSelectVoice = (voiceModel: VoiceModel) => {
		onSelectVoice(voiceModel);
		onOpenChange(false);
	};

	// Render voice model item
	const renderVoiceModelItem = (voiceModel: VoiceModel) => (
		<div
			key={voiceModel.customVoiceId}
			className={`flex cursor-pointer items-center justify-between rounded-lg border p-4 transition-colors ${
				selectedVoiceId === voiceModel.customVoiceId ? "border-primary bg-primary/5" : "border-border hover:bg-muted/50"
			}`}
			onClick={() => handleSelectVoice(voiceModel)}
		>
			<div className="flex-1">
				<div className="flex items-center gap-2">
					<h4 className="font-medium">{voiceModel.name}</h4>
					{selectedVoiceId === voiceModel.customVoiceId && <CheckIcon className="text-primary h-4 w-4" />}
				</div>
				{voiceModel.description && <p className="text-muted-foreground mt-1 text-sm">{voiceModel.description}</p>}
			</div>

			{voiceModel.mediaPath && (
				<Button
					variant="outline"
					size="sm"
					onClick={(e) => {
						e.stopPropagation();
						handlePreview(voiceModel);
					}}
					className="ml-4"
				>
					{playingVoiceId === voiceModel.customVoiceId && isPlaying ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
				</Button>
			)}
		</div>
	);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[80vh] max-w-2xl">
				<DialogHeader>
					<DialogTitle>Select Voice Model</DialogTitle>
				</DialogHeader>

				{/* Hidden waveform for audio preview */}
				<div ref={waveformRef} className="hidden" />

				<Tabs defaultValue="library" className="w-full">
					<TabsList className="grid w-full grid-cols-2">
						<TabsTrigger value="library">Library</TabsTrigger>
						<TabsTrigger value="design">Design</TabsTrigger>
					</TabsList>

					<TabsContent value="library" className="mt-4">
						<ScrollArea className="h-[400px] pr-4">
							{loading ? (
								<div className="flex h-32 items-center justify-center">
									<div className="text-muted-foreground">Loading...</div>
								</div>
							) : voiceModels.library.length === 0 ? (
								<div className="flex h-32 items-center justify-center">
									<div className="text-muted-foreground">No library voices available</div>
								</div>
							) : (
								<div className="space-y-3">{voiceModels.library.map(renderVoiceModelItem)}</div>
							)}
						</ScrollArea>
					</TabsContent>

					<TabsContent value="design" className="mt-4">
						<ScrollArea className="h-[400px] pr-4">
							{loading ? (
								<div className="flex h-32 items-center justify-center">
									<div className="text-muted-foreground">Loading...</div>
								</div>
							) : voiceModels.design.length === 0 ? (
								<div className="flex h-32 items-center justify-center">
									<div className="text-muted-foreground">No design voices available</div>
								</div>
							) : (
								<div className="space-y-3">{voiceModels.design.map(renderVoiceModelItem)}</div>
							)}
						</ScrollArea>
					</TabsContent>
				</Tabs>
			</DialogContent>
		</Dialog>
	);
}
