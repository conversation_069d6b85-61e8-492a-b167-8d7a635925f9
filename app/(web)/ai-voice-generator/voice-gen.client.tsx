"use client";

import { use<PERSON><PERSON>back, useR<PERSON>, useState, useEffect } from "react";
import { Button, buttonVariants } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { PauseIcon, PlayIcon, SparklesIcon, SettingsIcon } from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GENERATE_VOICE } from "@/lib/track-events";
import { useWavesurfer } from "@wavesurfer/react";
import VoiceModelDialog from "@/app/(web)/ai-voice-generator/voice-model-dialog";
import { OSS_URL_HOST } from "@/lib/constants";

interface VoiceModel {
	id: string;
	customVoiceId: string;
	name: string;
	description?: string;
	mediaPath?: string | null;
	textPreview?: string;
	visibility: boolean;
	userId?: string | null;
}

export default function VoiceGenClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [textPreview, setTextPreview] = useState("");
	const [selectedVoiceModel, setSelectedVoiceModel] = useState<VoiceModel>({
		id: "Wise_Woman",
		customVoiceId: "Wise_Woman",
		name: "Wise Woman",
		description: "A mature and wise female voice",
		mediaPath: null,
		visibility: true,
		userId: null,
	});
	const [voiceModelDialogOpen, setVoiceModelDialogOpen] = useState(false);

	const [submitting, setSubmitting] = useState(false);
	const [generatedVoice, setGeneratedVoice] = useState<any>(null);
	const handleVoiceGenerate = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const textPreviewTrim = textPreview.trim();

		// Check text length limits
		const maxLength = userHasPaid ? 1000 : 100;
		if (textPreviewTrim.length > maxLength) {
			toast.error(`Text is too long. Maximum ${maxLength} characters allowed.`);
			return;
		}

		if (!textPreviewTrim) {
			toast.error("Please enter some text to generate voice.");
			return;
		}

		sendGTMEvent({
			event: EVENT_GENERATE_VOICE,
			membership_level: user?.membershipLevel,
		});

		try {
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/voice/generate", {
				method: "POST",
				body: {
					textPreview: textPreviewTrim,
					customVoiceId: selectedVoiceModel.customVoiceId,
				},
			});
			handleError(status, message);
			refreshUser();

			setGeneratedVoice(resultUrl);
			toast.success("Voice generated successfully!");
		} catch (error: any) {
			console.error("Failed to generate voice:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const previewAudio = generatedVoice?.mediaPath
		? generatedVoice.mediaPath.startsWith("http")
			? generatedVoice.mediaPath
			: `${OSS_URL_HOST}${generatedVoice.mediaPath}`
		: "";
	const waveformRef = useRef<HTMLDivElement>(null);
	const { wavesurfer, isPlaying } = useWavesurfer({
		container: waveformRef,
		height: 88,
		waveColor: "#d4d4d8",
		progressColor: "#71717a",
		cursorColor: "#10b981",
		barWidth: 2,
		barGap: 1,
		barRadius: 2,
		url: previewAudio,
	});

	const onPlayPause = useCallback(() => {
		wavesurfer && wavesurfer.playPause();
	}, [wavesurfer]);

	const handleSelectVoice = (voiceModel: VoiceModel) => {
		setSelectedVoiceModel(voiceModel);
	};

	// Preload voice models when component mounts
	useEffect(() => {
		const preloadVoiceModels = async () => {
			try {
				// Silently preload voice models in the background
				await ofetch("/api/v1/voice/models");
			} catch (error) {
				// Silently fail - the dialog will handle loading when opened
				console.log("Preload voice models failed:", error);
			}
		};

		preloadVoiceModels();
	}, []);

	const maxTextLength = userHasPaid ? 1000 : 100;

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="w-full space-y-6">
				{/* Voice Model Selection */}
				<div className="bg-muted w-full rounded-xl px-4 py-4 backdrop-blur-3xl md:rounded-3xl">
					<Label htmlFor="voiceModel" className="mb-3 block text-base font-semibold">
						Voice Model
					</Label>
					<Button variant="outline" onClick={() => setVoiceModelDialogOpen(true)} className="h-12 w-full justify-between">
						<div className="flex items-center gap-2">
							<span>{selectedVoiceModel.name}</span>
							{selectedVoiceModel.description && <span className="text-muted-foreground text-sm">- {selectedVoiceModel.description}</span>}
						</div>
						<SettingsIcon className="h-4 w-4" />
					</Button>
				</div>

				{/* Text Input */}
				<div className="bg-muted w-full rounded-xl px-4 py-4 backdrop-blur-3xl md:rounded-3xl">
					<Label htmlFor="textPreview" className="mb-3 block text-base font-semibold">
						Text to Generate *
					</Label>
					<Textarea
						id="textPreview"
						placeholder="Enter the text that will be used to generate voice (e.g., 'Hello, this is a sample of my custom voice. How does it sound?')"
						value={textPreview}
						maxLength={maxTextLength}
						onChange={(e) => setTextPreview(e.target.value)}
						className="min-h-[80px] resize-none border-none px-0 shadow-none placeholder:text-base focus-visible:text-base focus-visible:ring-0 md:text-base [&::-webkit-scrollbar]:hidden"
					/>
					<div className="mt-2 flex items-center justify-between">
						<div className="text-muted-foreground text-xs">
							{textPreview.length}/{maxTextLength} characters
						</div>
						{!userHasPaid && <div className="text-xs text-orange-600">Upgrade for 1000 characters</div>}
					</div>
				</div>

				<div className="flex items-center justify-between gap-4">
					<SubmitButton
						isSubmitting={submitting}
						onClick={handleVoiceGenerate}
						disabled={submitting || !textPreview.trim()}
						className="bg-foreground hover:bg-foreground h-10"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="md:block">Generate Speech</span>
					</SubmitButton>
				</div>
			</div>

			{/* Audio Preview Section */}
			<div className="group relative flex h-[180px] w-full flex-col justify-between rounded-lg border-zinc-700 px-3 py-1.5 md:h-[200px] dark:bg-zinc-900/30">
				<div className="flex flex-row items-center justify-between">
					<div className="flex flex-col">
						{generatedVoice?.description && <p className="text-muted-foreground line-clamp-1 text-xs">{generatedVoice.description}</p>}
					</div>
					{generatedVoice && <div className="text-muted-foreground text-xs">Ready</div>}
				</div>
				<div ref={waveformRef} className="h-auto" />
				<div className="flex flex-row items-center justify-between">
					<Button
						size="icon"
						variant="secondary"
						onClick={onPlayPause}
						disabled={!previewAudio}
						className="rounded-full bg-zinc-700/70 text-zinc-200 hover:bg-zinc-700/90 disabled:opacity-50"
					>
						{isPlaying ? <PauseIcon className="fill-current" /> : <PlayIcon className="fill-current" />}
					</Button>
					{generatedVoice && <div className="text-muted-foreground text-xs">"{generatedVoice.textPreview}"</div>}
				</div>
			</div>

			{/* Voice Model Selection Dialog */}
			<VoiceModelDialog
				open={voiceModelDialogOpen}
				onOpenChange={setVoiceModelDialogOpen}
				selectedVoiceId={selectedVoiceModel.customVoiceId}
				onSelectVoice={handleSelectVoice}
			/>
		</div>
	);
}
