import { UserInfoDB } from "@/@types/user";
import { getUserRealtime, resetUserOnetimeCredits, resetUserFreeCreditsMonthly, resetUserSubscriptionForYearly } from "./utils-user.server";
import { User } from "./db/schema.server";

export async function refreshUser(
	userId: string,
	checkOptions?: {
		existUser?: User;
	},
): Promise<UserInfoDB | null> {
	let user: User | null;
	if (checkOptions?.existUser) {
		user = checkOptions.existUser;
	} else {
		user = await getUserRealtime(userId);
	}
	if (!user) return null;

	user = await resetUserOnetimeCredits(user);
	// Reset free credits every month for free user
	user = await resetUserFreeCreditsMonthly(user);
	// Reset subscription every month for yearly user
	user = await resetUserSubscriptionForYearly(user);
	/** 注：在webhook中更新subscription完成过期异常处理 **/

	// 5. Return new user data
	if (user) {
		return {
			id: user.id,
			name: user.name,
			email: user.email,
			image: user.image,
			membershipId: user.membershipId,
			membershipFormatted: user.membershipFormatted,
			creditFree: user.creditFree,
			creditFreeEndsAt: user.creditFreeEndsAt,
			creditOneTime: user.creditOneTime,
			creditOneTimeModel: user.creditOneTimeModel,
			creditOneTimeEndsAt: user.creditOneTimeEndsAt,
			creditSubscription: user.creditSubscription,
			creditSubscriptionModel: user.creditSubscriptionModel,
			creditSubscriptionEndsAt: user.creditSubscriptionEndsAt,

			subscriptionId: user.subscriptionId,
			subscriptionPeriod: user.subscriptionPeriod,
			// subscriptionInvoiceEndsAt: user.subscriptionInvoiceEndsAt,
			subscriptionExpireAt: user.subscriptionExpireAt,
		};
	} else {
		return null;
	}
}
