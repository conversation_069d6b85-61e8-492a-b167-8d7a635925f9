import { getKVKeyUser } from "@/lib/utils";
import { DURATION_1_DAY } from "@/lib/constants";
import { getDB } from "./db/db-client.server";
import { NewUserCreditsHistory, User, userCreditsHistorySchema, userSchema } from "./db/schema.server";
import { eq } from "drizzle-orm";
import { deleteValue, getValue, setValue } from "./kv/redis-upstash.server";
import superjson from "superjson";
import { addMonths, endOfMonth } from "date-fns";
import { CreditHistoryType, MembershipID, membershipMapping, MembershipPeriodNone, MembershipPeriodYear } from "@/@types/membership-type";

export async function getUser(userUid: string | null | undefined): Promise<User | null> {
	if (!userUid) return null;

	//先从kv中获取
	let cacheUserKey = getKVKeyUser(userUid);
	// console.log("cacheUserKey:", cacheUserKey);
	const kvData = (await getValue(cacheUserKey)) as any;
	let user: User | null = null;

	if (kvData) {
		try {
			user = superjson.deserialize(kvData) as User;
			return user;
		} catch (error) {
			console.log("[getUser] parse data from kv error:", error);
		}
	}

	//再从db中获取
	const db = getDB();
	const users: User[] = await db.select().from(userSchema).where(eq(userSchema.id, userUid));
	if (users.length === 0) return null;
	user = users[0];

	await setValue(cacheUserKey, superjson.stringify(user), DURATION_1_DAY);
	return user;
}

export async function getUserRealtime(userUid?: string | null | undefined): Promise<User | null> {
	if (!userUid) return null;

	const db = getDB();
	const [user]: User[] = await db.select().from(userSchema).where(eq(userSchema.id, userUid));
	if (!user) {
		return null;
	}

	return user;
}

// reset user subscription membership to free
export async function revokeUserMembershipWithSubscription(userId: string | null | undefined) {
	if (!userId) return;

	const user = await getUserRealtime(userId);
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: userId,
		type: CreditHistoryType.ExpireSubscription,
		creditsSubscription: user?.creditSubscription,
		creditsSubscriptionModel: user?.creditSubscriptionModel,
		remark: `Expire creditSubscription for subscription revoked. userId: ${userId}`,
	};

	const db = getDB();
	await db.transaction(async (tx) => {
		await tx.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
		await tx
			.update(userSchema)
			.set({
				membershipId: MembershipID.Free,
				membershipFormatted: membershipMapping[MembershipID.Free].name,

				creditSubscription: 0,
				creditSubscriptionModel: 0,

				subscriptionPeriod: MembershipPeriodNone.value,
				subscriptionId: null,
				subscriptionInvoiceEndsAt: null,
				subscriptionExpireAt: null,
			})
			.where(eq(userSchema.id, userId));
	});
}

export async function resetUserSubscriptionForYearly(user: User): Promise<User> {
	// the user does not have subscription, return directly
	if (!user.subscriptionId) return user;

	// the user subscription is free or not yearly, return directly
	if (user.membershipId === MembershipID.Free || user.subscriptionPeriod !== MembershipPeriodYear.value) return user;

	// the user subscription current period ends at is in the future(not expired), return directly
	const currentDate = new Date();
	if (!user.creditSubscriptionEndsAt || user.creditSubscriptionEndsAt > currentDate) return user;

	// renew for yearly
	// console.log("----refreshUser isRenewForYear for subscription membership");
	// 1. Get next renew date
	let nextRenewDate = addMonths(user.creditSubscriptionEndsAt, 1); // increment month by 1
	// Keep adding months until nextRenewDate is in the future
	while (nextRenewDate < currentDate) {
		nextRenewDate = addMonths(nextRenewDate, 1);
	}

	// 2. Get new credits
	const credits = membershipMapping[user.membershipId as MembershipID].credits;
	const creditsModel = membershipMapping[user.membershipId as MembershipID].creditsModel;

	// 3. Insert credits change record to db: user_credits_history
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: user.id,
		type: CreditHistoryType.ResetSubscription,
		creditsSubscription: credits,
		creditsSubscriptionModel: creditsModel,
		remark: `Monthly creditSubscription reset for yearly subscriptions. SubscriptionId: ${user.subscriptionId}, last creditSubscription: ${user.creditSubscription}`,
	};

	// 4. Update tokens to db for subscription yearly user
	const db = getDB();
	const updatedUser = await db.transaction(async (tx) => {
		await tx.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
		const [updatedUser]: User[] = await tx
			.update(userSchema)
			.set({
				creditSubscription: credits,
				creditSubscriptionModel: creditsModel,
				creditSubscriptionEndsAt: nextRenewDate,
			})
			.where(eq(userSchema.id, user.id))
			.returning();
		return updatedUser;
	});

	await deleteValue(getKVKeyUser(user.id));

	return updatedUser;
}

// 每月重置免费用户免费额度
export async function resetUserFreeCreditsMonthly(user: User): Promise<User> {
	// the user has subscription, return directly
	if (user.subscriptionId) return user;

	// the user is not free, return directly
	if (user.membershipId !== MembershipID.Free) return user;

	// the user free credits is not expired(until the end of current month), return directly
	const currentDate = new Date();
	if (user.creditFreeEndsAt && user.creditFreeEndsAt > currentDate) return user;

	// reset free credits monthly
	// 1. Get new credits
	const freeCredits = membershipMapping[MembershipID.Free].credits;

	// 2. Insert credits change record to db: user_credits_history
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: user.id,
		type: CreditHistoryType.ResetFree,
		creditsFree: freeCredits,
		remark: "Reset free credits for free user at the end of current month.",
	};

	// 3. Update tokens to db for free user
	const db = getDB();
	const updatedUser = await db.transaction(async (tx) => {
		await tx.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
		const [updatedUser]: User[] = await tx
			.update(userSchema)
			.set({
				creditFree: freeCredits,
				creditFreeEndsAt: endOfMonth(currentDate),
			})
			.where(eq(userSchema.id, user.id))
			.returning();
		return updatedUser;
	});

	await deleteValue(getKVKeyUser(user.id));

	return updatedUser;
}
//（本项目没有使用）
export async function expireUserFreeCredits(user: User): Promise<User> {
	if (user.creditFree === 0) return user;
	if (!user.creditFreeEndsAt) return user;

	// the user free credits is not expired(until the end of current month), return directly
	if (user.creditFreeEndsAt > new Date()) return user;

	// 1. Insert credits change record to db: user_credits_history
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: user.id,
		type: CreditHistoryType.ExpireFree,
		creditsFree: user.creditFree,
		remark: "Expire free credits for free user.",
	};

	// 2. Update tokens to db for free user
	const db = getDB();
	const updatedUser = await db.transaction(async (tx) => {
		await tx.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
		const [updatedUser]: User[] = await tx
			.update(userSchema)
			.set({
				creditFree: 0,
				creditFreeEndsAt: null,
			})
			.where(eq(userSchema.id, user.id))
			.returning();
		return updatedUser;
	});

	await deleteValue(getKVKeyUser(user.id));

	return updatedUser;
}

export async function resetUserSubscriptionResume(userId: string) {
	const db = getDB();
	await db
		.update(userSchema)
		.set({
			subscriptionExpireAt: null,
		})
		.where(eq(userSchema.id, userId));

	await deleteValue(getKVKeyUser(userId));
}

// reset user onetime credits after expired
export async function resetUserOnetimeCredits(user: User): Promise<User> {
	if (!user.creditOneTimeEndsAt) return user;
	if (user.creditOneTimeEndsAt > new Date()) return user;

	// 2. Update tokens to db for free user
	// 3. Insert credits change record to db: user_credits_history
	const db = getDB();
	const updatedUser: User = await db.transaction(async (tx) => {
		await tx.insert(userCreditsHistorySchema).values({
			userId: user.id,
			type: CreditHistoryType.ExpireOnetime,
			creditsOneTime: user.creditOneTime,
			creditsOneTimeModel: user.creditOneTimeModel,
			remark: `Expire onetime credits: ${user.creditOneTimeEndsAt}`,
		} as NewUserCreditsHistory);
		const [updatedUser]: User[] = await tx
			.update(userSchema)
			.set({
				membershipId: MembershipID.Free,
				membershipFormatted: membershipMapping[MembershipID.Free].name,
				creditOneTime: 0,
				creditOneTimeModel: 0,
				creditOneTimeEndsAt: null,
			})
			.where(eq(userSchema.id, user.id))
			.returning();
		return updatedUser;
	});

	await deleteValue(getKVKeyUser(user.id));

	return updatedUser;
}
