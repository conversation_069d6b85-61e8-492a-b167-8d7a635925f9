import {
	Account,
	accountSchema,
	NewAccount,
	NewSession,
	NewUser,
	NewUserCreditsHistory,
	NewVerification,
	Session,
	sessionSchema,
	User,
	UserCreditsHistory,
	userCreditsHistorySchema,
	userSchema,
	Verification,
	verificationSchema,
} from "./schema-user.server";
import {
	voiceModelTaskSchema,
	VoiceModelTask,
	NewVoiceModelTask,
	voiceModelSchema,
	VoiceModel,
	NewVoiceModel,
	mediaVoiceSchema,
	MediaVoice,
	NewMediaVoice,
	aiModelSchema,
	AIModel,
	NewAIModel,
	// MediaHead,
	// mediaHeadSchema,
	// MediaTask,
	// mediaTaskSchema,
	// NewMediaHead,
	// NewMediaTask,
} from "./schema-media.server";
import { NewOrder, NewSubscription, Order, orderSchema, Subscription, subscriptionSchema } from "./schema-billing.server";
import {
	blogCategorySchema,
	BlogCategory,
	NewBlogCategory,
	blogPostSchema,
	BlogPost,
	NewBlogPost,
	blogPostTranslationSchema,
	NewBlogPostTranslation,
	BlogPostTranslation,
	blogPostRelations,
	blogPostTranslationRelations,
} from "./schema-blog.server";
import {
	Changelog,
	changelogRelations,
	changelogSchema,
	ChangelogTranslation,
	changelogTranslationRelations,
	changelogTranslationSchema,
	NewChangelog,
	NewChangelogTranslation,
} from "./schema-changelog.server";

export {
	userSchema,
	type User,
	type NewUser,
	accountSchema,
	type NewAccount,
	type Account,
	sessionSchema,
	type NewSession,
	type Session,
	verificationSchema,
	type NewVerification,
	type Verification,
	userCreditsHistorySchema,
	type NewUserCreditsHistory,
	type UserCreditsHistory,
	voiceModelTaskSchema,
	type VoiceModelTask,
	type NewVoiceModelTask,
	voiceModelSchema,
	type VoiceModel,
	type NewVoiceModel,
	mediaVoiceSchema,
	type MediaVoice,
	type NewMediaVoice,
	aiModelSchema,
	type AIModel,
	type NewAIModel,
	// mediaTaskSchema,
	// type NewMediaTask,
	// type MediaTask,
	// mediaHeadSchema,
	// type NewMediaHead,
	// type MediaHead,
	orderSchema,
	type NewOrder,
	type Order,
	subscriptionSchema,
	type NewSubscription,
	type Subscription,
	blogCategorySchema,
	type NewBlogCategory,
	type BlogCategory,
	blogPostSchema,
	type NewBlogPost,
	type BlogPost,
	blogPostTranslationSchema,
	type NewBlogPostTranslation,
	type BlogPostTranslation,
	blogPostRelations,
	blogPostTranslationRelations,
	changelogSchema,
	type Changelog,
	type NewChangelog,
	changelogTranslationSchema,
	type ChangelogTranslation,
	type NewChangelogTranslation,
	changelogRelations,
	changelogTranslationRelations,
};
