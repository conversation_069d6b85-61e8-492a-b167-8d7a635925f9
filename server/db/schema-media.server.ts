import { sqliteTable, integer, text, index } from "drizzle-orm/sqlite-core";

// ==============voice model generate task ===============
export const aiModelSchema = sqliteTable("ai_model", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	title: text("title"),
	model: text("model"), // e.g. minimax/speech-02
	status: text("status").default("active").notNull(),
	remark: text("remark"),
	createdAt: integer("created_at", { mode: "timestamp_ms" })
		.$defaultFn(() => new Date())
		.notNull(),
	updatedAt: integer("updated_at", { mode: "timestamp_ms" })
		.$defaultFn(() => new Date())
		.$onUpdateFn(() => new Date())
		.notNull(),
});
export type AIModel = typeof aiModelSchema.$inferSelect;
export type NewAIModel = typeof aiModelSchema.$inferInsert;

// ==============voice model generate task ===============
export const voiceModelTaskSchema = sqliteTable(
	"voice_model_task",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),
		status: integer("status", { mode: "number" }).default(0).notNull(),

		customVoiceId: text("custom_voice_id"),
		thirdRequestId: text("third_request_id"), // API  request id

		visibility: integer("visibility", { mode: "boolean" }).default(false).notNull(),
		aiModelId: integer("ai_model_id").notNull(),
		prompt: text("prompt"),
		textPreview: text("text_preview"),
		name: text("name").notNull(),
		description: text("description"),
		languageCode: text("language_code"),

		creditsSources: text("credits_source"),
		ip: text("ip"),
		remark: text("remark"),
		error: text("error"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.notNull(),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date())
			.notNull(),
	},
	(table) => [
		index("idx_voice_model_task_user_uid").on(table.userId),
		index("idx_voice_model_task_third_request_id").on(table.thirdRequestId),
		index("idx_voice_model_task_status").on(table.status),
	],
);
export type VoiceModelTask = typeof voiceModelTaskSchema.$inferSelect;
export type NewVoiceModelTask = typeof voiceModelTaskSchema.$inferInsert;

export const voiceModelSchema = sqliteTable("voice_model", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),

	customVoiceId: text("custom_voice_id").unique().notNull(),
	userId: text("user_uid"),
	aiModelId: integer("ai_model_id").notNull(),
	visibility: integer("visibility", { mode: "boolean" }).default(false).notNull(),

	name: text("name").notNull(),
	description: text("description"),
	languageCode: text("language_code"),
	prompt: text("prompt"),
	textPreview: text("text_preview"),

	mediaPath: text("media_path"),

	creditsSources: text("credits_source"),
	taskId: integer("task_id"),
	remark: text("remark"),
	errorReason: text("error_reason"),
	createdAt: integer("created_at", { mode: "timestamp_ms" })
		.$defaultFn(() => new Date())
		.notNull(),
	updatedAt: integer("updated_at", { mode: "timestamp_ms" })
		.$defaultFn(() => new Date())
		.$onUpdateFn(() => new Date())
		.notNull(),
});
export type VoiceModel = typeof voiceModelSchema.$inferSelect;
export type NewVoiceModel = typeof voiceModelSchema.$inferInsert;

// ==============media voice result====================
export const mediaVoiceSchema = sqliteTable(
	"media_voice",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		uid: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),
		visibility: integer("visibility", { mode: "boolean" }).default(false).notNull(), // false: private, true: public
		status: integer("status", { mode: "number" }).default(0).notNull(), // like: generating, failed, success

		customVoiceId: text("custom_voice_id"),

		mediaPath: text("media_path"),

		creditsSources: text("credits_source"),
		remark: text("remark"),
		errorReason: text("error_reason"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_media_head_user_uid").on(table.userId), index("idx_media_head_status").on(table.status)],
);
export type MediaVoice = typeof mediaVoiceSchema.$inferSelect;
export type NewMediaVoice = typeof mediaVoiceSchema.$inferInsert;

// // ==============media generate task ===============
// export const mediaTaskSchema = sqliteTable(
// 	"media_task",
// 	{
// 		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
// 		parentTaskId: integer("parent_task_id", { mode: "number" }),

// 		userId: text("user_uid").notNull(),
// 		mediaType: text("media_type"),
// 		tool: text("tool"), // tool name, e.g. lipsync-video-with-audio

// 		model: text("model"),
// 		requestBody: text("request_body"),

// 		thirdRequestId: text("third_request_id"), // API  request id
// 		status: integer("status", { mode: "number" }).default(0).notNull(),
// 		visibility: integer("visibility", { mode: "boolean" }).default(false).notNull(),
// 		mediaHeadUid: text("media_head_uid"),

// 		creditsSources: text("credits_source"),
// 		ip: text("ip"),

// 		remark: text("remark"),
// 		error: text("error"),
// 		createdAt: integer("created_at", { mode: "timestamp_ms" })
// 			.$defaultFn(() => new Date())
// 			.notNull(),
// 		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
// 			.$defaultFn(() => new Date())
// 			.$onUpdateFn(() => new Date())
// 			.notNull(),
// 	},
// 	(table) => [
// 		index("idx_media_task_user_uid").on(table.userId),
// 		index("idx_media_task_parent_task_id").on(table.parentTaskId),
// 		index("idx_media_task_third_request_id").on(table.thirdRequestId),
// 		index("idx_media_task_status").on(table.status),
// 	],
// );
// export type MediaTask = typeof mediaTaskSchema.$inferSelect;
// export type NewMediaTask = typeof mediaTaskSchema.$inferInsert;
