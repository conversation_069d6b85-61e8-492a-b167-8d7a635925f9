import { WAN_2_2_TURBO } from "@/config/video-models-config";
import { falGenVideoWithWebhook } from "./fal-config.server";

export async function genWAN2Point2FromFal(model: string, prompt: string, aspectRatio: string, resolution: string, image?: string | null): Promise<string> {
	// let falAIEndPoint = "fal-ai/wan/v2.2-5b";
	let falAIEndPoint = "fal-ai/wan/v2.2-a14b";
	let payload: any = {
		prompt: prompt,
		resolution: resolution,
		aspect_ratio: aspectRatio,
	};
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/image-to-video";
	} else {
		falAIEndPoint += "/text-to-video";
	}
	if (model === WAN_2_2_TURBO.model) {
		falAIEndPoint += "/turbo";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai wan 2.2 falAIEndPoint: ", falAIEndPoint);
		console.log("fal.ai wan 2.2 payload: ", payload);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
