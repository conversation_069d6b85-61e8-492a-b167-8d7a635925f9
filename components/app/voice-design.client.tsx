"use client";

import { use<PERSON><PERSON>back, useRef, useState } from "react";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { PauseIcon, PlayIcon, SparklesIcon } from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { cn } from "@/lib/utils";
import { Icons } from "../icon/icons";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_DESIGN_VOICE } from "@/lib/track-events";
import { Hint } from "../ui/custom/hint";
import { useWavesurfer } from "@wavesurfer/react";
import { MediaResultStatus } from "@/@types/media/media-type";

export default function VoiceDesignClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [visibility, setVisibility] = useState<string>("public");
	const [prompt, setPrompt] = useState("");
	const [textPreview, setTextPreview] = useState("");

	const [submitting, setSubmitting] = useState(false);
	const [generatedVoice, setGeneratedVoice] = useState<any>(null);
	const handleVoiceDesign = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promptTrim = prompt.trim();
		const textPreviewTrim = textPreview.trim();

		if (!promptTrim || !textPreviewTrim) {
			toast.error("Please fill in all required fields (prompt and text preview).");
			return;
		}

		sendGTMEvent({
			event: EVENT_DESIGN_VOICE,
			membership_level: user?.membershipLevel,
		});

		try {
			setSubmitting(true);
			const { status, message, request_id } = await ofetch("/api/v1/voice/design", {
				method: "POST",
				body: {
					prompt: promptTrim,
					textPreview: textPreviewTrim,
					visibility: visibility,
				},
			});
			handleError(status, message);
			refreshUser();

			let taskStatus = MediaResultStatus.InProgress;
			let taskError = null;
			await new Promise((resolve) => setTimeout(resolve, 5000));

			// get task status
			while (taskStatus !== MediaResultStatus.Completed && taskStatus !== MediaResultStatus.Failed) {
				await new Promise((resolve) => setTimeout(resolve, 5000));
				let {
					status: request_status,
					message: request_message,
					taskStatus: request_taskStatus,
					taskError: request_taskError,
					voiceModel,
				} = await ofetch("/api/v1/voice/design/status", {
					method: "POST",
					body: { id: request_id },
				});
				handleError(request_status, request_message);
				taskStatus = request_taskStatus;
				taskError = request_taskError;

				if (taskStatus === MediaResultStatus.Completed && voiceModel) {
					setGeneratedVoice(voiceModel);
				}
			}
			if (taskStatus === MediaResultStatus.Failed) {
				if (taskError) {
					throw new Error(taskError);
				}
				throw new Error("Voice design failed. Please try again or contact support.");
			}

			if (taskStatus === MediaResultStatus.Completed) {
				toast.success("Voice design completed successfully!");
			}
		} catch (error: any) {
			console.error("Failed to design voice:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const previewAudio = generatedVoice?.mediaPath || "";
	const waveformRef = useRef<HTMLDivElement>(null);
	const { wavesurfer, isPlaying } = useWavesurfer({
		container: waveformRef,
		height: 88,
		waveColor: "#d4d4d8",
		progressColor: "#71717a",
		cursorColor: "#10b981",
		barWidth: 2,
		barGap: 1,
		barRadius: 2,
		url: previewAudio,
	});

	const onPlayPause = useCallback(() => {
		wavesurfer && wavesurfer.playPause();
	}, [wavesurfer]);

	return (
		<div className="flex w-full flex-col items-center gap-6">
			{/* Main Form Section */}
			<div className="w-full space-y-6">
				{/* Prompt Section - Visual Focus */}
				<div className="bg-muted w-full rounded-xl px-4 py-4 backdrop-blur-3xl md:rounded-3xl">
					<Label htmlFor="prompt" className="mb-3 block text-base font-semibold">
						Voice Design Prompt *
					</Label>
					<Textarea
						id="prompt"
						placeholder="Describe the voice you want to create (e.g., 'A warm, friendly female voice with a slight British accent, perfect for storytelling')"
						value={prompt}
						maxLength={1000}
						onChange={(e) => setPrompt(e.target.value)}
						className="min-h-[100px] resize-none border-none px-0 shadow-none placeholder:text-base focus-visible:text-base focus-visible:ring-0 md:text-base [&::-webkit-scrollbar]:hidden"
					/>
					<div className="text-muted-foreground mt-2 text-xs">{prompt.length}/1000 characters</div>
				</div>

				{/* Text Preview Section - Visual Focus */}
				<div className="bg-muted w-full rounded-xl px-4 py-4 backdrop-blur-3xl md:rounded-3xl">
					<Label htmlFor="textPreview" className="mb-3 block text-base font-semibold">
						Text to Preview *
					</Label>
					<Textarea
						id="textPreview"
						placeholder="Enter the text that will be used to generate a preview of your voice (e.g., 'Hello, this is a sample of my custom voice. How does it sound?')"
						value={textPreview}
						maxLength={500}
						onChange={(e) => setTextPreview(e.target.value)}
						className="min-h-[80px] resize-none border-none px-0 shadow-none placeholder:text-base focus-visible:text-base focus-visible:ring-0 md:text-base [&::-webkit-scrollbar]:hidden"
					/>
					<div className="text-muted-foreground mt-2 text-xs">{textPreview.length}/500 characters</div>
				</div>

				{/* Controls Section */}
				<div className="flex items-center justify-between gap-4">
					<div className="flex flex-wrap items-center gap-2">
						<Hint label={`${visibility === "public" ? "Public: Anyone can see and use" : "Private: Only you can see and use"}`}>
							<div
								className={cn(
									buttonVariants({ variant: "secondary", size: "sm" }),
									"hover:ring-input h-8 cursor-pointer gap-0.5 rounded-full bg-white py-0 text-xs shadow-none hover:bg-white hover:ring-2",
								)}
								onClick={() => {
									if (visibility === "public" && !userHasPaid) {
										setPlanBoxOpen(true);
										return;
									}
									if (visibility === "public") {
										setVisibility("private");
									} else {
										setVisibility("public");
									}
								}}
							>
								{visibility === "public" ? "Public" : "Private"}
								{!userHasPaid && <Icons.Lock className="size-3.5" />}
							</div>
						</Hint>
					</div>

					<SubmitButton
						isSubmitting={submitting}
						onClick={handleVoiceDesign}
						disabled={submitting || !prompt.trim() || !textPreview.trim()}
						className="bg-foreground hover:bg-foreground h-10"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="md:block">Generate Voice</span>
					</SubmitButton>
				</div>
			</div>

			{/* Audio Preview Section */}
			<div className="group relative flex h-[180px] w-full flex-col justify-between rounded-lg border-zinc-700 px-3 py-1.5 md:h-[200px] dark:bg-zinc-900/30">
				<div className="flex flex-row items-center justify-between">
					<div className="flex flex-col">
						<p className="line-clamp-1 text-sm font-medium">{generatedVoice ? generatedVoice.name : "Voice Preview"}</p>
						{generatedVoice?.description && <p className="text-muted-foreground line-clamp-1 text-xs">{generatedVoice.description}</p>}
					</div>
					{generatedVoice && <div className="text-muted-foreground text-xs">Ready</div>}
				</div>
				<div ref={waveformRef} className="h-auto" />
				<div className="flex flex-row items-center justify-between">
					<Button
						size="icon"
						variant="secondary"
						onClick={onPlayPause}
						disabled={!previewAudio}
						className="rounded-full bg-zinc-700/70 text-zinc-200 hover:bg-zinc-700/90 disabled:opacity-50"
					>
						{isPlaying ? <PauseIcon className="fill-current" /> : <PlayIcon className="fill-current" />}
					</Button>
					{generatedVoice && <div className="text-muted-foreground text-xs">"{generatedVoice.textPreview}"</div>}
				</div>
			</div>
		</div>
	);
}
